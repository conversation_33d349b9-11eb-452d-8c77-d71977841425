# <PERSON>'s Portfolio

My personal portfolio site built with Next.js and React. Features a retro vaporwave/outrun aesthetic with some custom animations and effects.

## Tech Stack

- Next.js 15
- React 19
- TypeScript
- Tailwind CSS
- Framer Motion for animations
- Deployed on Vercel

## Development

```bash
# Install dependencies
pnpm install

# Run development server
pnpm dev

# Build for production
pnpm build
```

## Project Structure

- `/app` - Next.js app router pages
- `/components` - React components
- `/public` - Static assets
- `/styles` - Global CSS

## Notes

The site uses a custom VHS-style glitch effect for the hero section and intersection observer for scroll animations. The design is inspired by 80s/90s vaporwave and outrun aesthetics.
